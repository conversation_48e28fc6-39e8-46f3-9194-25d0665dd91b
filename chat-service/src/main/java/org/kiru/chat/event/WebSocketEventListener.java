package org.kiru.chat.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.chat.application.service.WebSocketUserService;
import org.kiru.core.chat.message.domain.TranslateLanguage;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;
import org.springframework.web.socket.messaging.SessionSubscribeEvent;

import java.util.Arrays;
import java.util.List;

import static java.util.Objects.requireNonNull;

@Component
@RequiredArgsConstructor
@Slf4j
public class WebSocketEventListener {
    private static final String TRANSLATION_QUEUE_PREFIX = "/queue/translate";
    private static final String CONNECT_ID = "userId";
    private final WebSocketUserService webSocketUserService;
    private final ApplicationEventPublisher eventPublisher;

    @EventListener
    public void handleWebSocketConnectListener(SessionConnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String userId = (String) requireNonNull(headerAccessor.getSessionAttributes()).get(CONNECT_ID);
        webSocketUserService.updateUserConnectionStatus(userId, true);
        // 사용자가 연결을 설정하면 활성 채팅방 정보도 설정됩니다. (WebSocketUserService에서 처리)
    }

    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String userId = (String) requireNonNull(headerAccessor.getSessionAttributes()).get(CONNECT_ID);
        webSocketUserService.updateUserConnectionStatus(userId, false);
        // 사용자가 연결을 끊으면 활성 채팅방 정보도 제거됩니다. (WebSocketUserService에서 처리)
    }

    @EventListener
    public void handleSubscribeEvent(SessionSubscribeEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String destination = headerAccessor.getDestination();
        String userId = (String) requireNonNull(headerAccessor.getSessionAttributes()).get(CONNECT_ID);

        // 채팅방 구독인 경우 처리
        if (destination != null && destination.startsWith("/topic/")) {
            try {
                Long roomId = Long.parseLong(destination.substring(destination.lastIndexOf('/') + 1));
                webSocketUserService.setUserInChatRoom(userId, roomId);
                log.info("User {} subscribed to chat room {} | destination={}", userId, roomId, destination);
            } catch (NumberFormatException e) {
                log.error("Failed to parse roomId from destination: {} | userId={}", destination, userId, e);
            }
        }

        // 번역 구독인 경우에만 처리
        if (destination != null && destination.contains(TRANSLATION_QUEUE_PREFIX)) {
            String targetLanguage = requireNonNull(headerAccessor.getFirstNativeHeader("targetLanguage"),
                    "Target language must be provided");
            String messageIds = requireNonNull(headerAccessor.getFirstNativeHeader("messageIds")
                    , "Message IDs must be provided");
            if (!messageIds.isEmpty()) {
                // 쉼표로 구분된 메시지 ID 문자열을 리스트로 변환
                List<Long> messageIdList = Arrays.stream(messageIds.split(","))
                        .map(Long::parseLong)
                        .toList();
                // 사용자의 번역 언어 설정을 업데이트
                webSocketUserService.updateUserTranslationPreference(userId, targetLanguage);
                eventPublisher.publishEvent(
                        new UserTranslateSubscribeEvent(userId, TranslateLanguage.valueOf(targetLanguage),
                                messageIdList));
                log.info("User subscribed to translation service | userId={} | language={} | messageCount={}",
                        userId, targetLanguage, messageIdList.size());
            }
        }
    }
}
