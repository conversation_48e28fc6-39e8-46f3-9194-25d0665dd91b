plugins {
    id 'java'
    id 'org.springframework.boot' version '3.3.4'
    id 'io.spring.dependency-management' version '1.1.6'
}

group = 'org.kiru'
version = '0.0.1-SNAPSHOT'


java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

ext {
    set('springCloudVersion', "2023.0.3")
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.redisson:redisson-spring-boot-starter:3.41.0'
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.boot:spring-boot-starter-web'


    implementation group: 'io.jsonwebtoken', name: 'jjwt-api', version: '0.11.5'
    implementation group: 'io.jsonwebtoken', name: 'jjwt-impl', version: '0.11.5'
    implementation group: 'io.jsonwebtoken', name: 'jjwt-jackson', version: '0.11.5'


    implementation("software.amazon.awssdk:bom:2.21.0")
    implementation("software.amazon.awssdk:s3:2.21.0")

    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'io.github.openfeign:feign-micrometer'

    // ResilienceJ 의존성 추가

    testImplementation 'io.github.autoparams:autoparams-mockito:9.0.0'
    testImplementation 'io.github.autoparams:autoparams:9.0.0'


    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'

    implementation 'io.micrometer:micrometer-tracing-bridge-otel'
    implementation "io.opentelemetry:opentelemetry-exporter-otlp"

    // core 모듈 의존성 추가
    implementation project(':core')
    
    // QueryDSL 의존성
    implementation 'com.querydsl:querydsl-jpa:5.0.0:jakarta'
    annotationProcessor "com.querydsl:querydsl-apt:5.0.0:jakarta"
    annotationProcessor "jakarta.annotation:jakarta.annotation-api"
    annotationProcessor "jakarta.persistence:jakarta.persistence-api"

    compileOnly 'org.projectlombok:lombok'
    runtimeOnly 'org.postgresql:postgresql'
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    runtimeOnly 'io.micrometer:micrometer-registry-prometheus'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    runtimeOnly 'io.netty:netty-resolver-dns-native-macos:4.1.104.Final:osx-aarch_64'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

// QueryDSL Q 클래스 생성 설정
def querydslDir = layout.buildDirectory.dir("generated/querydsl")

tasks.withType(JavaCompile).configureEach {
    options.getGeneratedSourceOutputDirectory().set(querydslDir)
}

sourceSets {
    main.java.srcDirs += [querydslDir]
}

clean {
    delete querydslDir
}
