package org.kiru.core.common;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.time.LocalDateTime;

@Getter
@AllArgsConstructor(staticName = "of")
public class CursorPageRequest {
    private final Long cursorId;
    private final LocalDateTime cursorCreatedAt;
    private final int size;
    
    private static final int DEFAULT_SIZE = 20;
    private static final int MAX_SIZE = 100;

    public CursorPageRequest(Long cursorId, LocalDateTime cursorCreatedAt, Integer size) {
        this.cursorId = cursorId;
        this.cursorCreatedAt = cursorCreatedAt;
        this.size = validateSize(size);
    }

    private int validateSize(Integer size) {
        if (size == null || size <= 0) {
            return DEFAULT_SIZE;
        }
        return Math.min(size, MAX_SIZE);
    }
} 