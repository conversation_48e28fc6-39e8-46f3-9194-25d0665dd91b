package org.kiru.chat.application.port.in;

import org.kiru.core.common.CursorPageRequest;
import org.kiru.core.common.CursorPageResponse;
import org.kiru.core.chat.chatroom.domain.ChatRoom;

public interface GetChatRoomUseCase {
    ChatRoom findRoomById(Long roomId, Long userId, boolean isUserAdmin);
    CursorPageResponse<ChatRoom> findRoomsByUserId(Long userId, CursorPageRequest cursorPageRequest);
    ChatRoom getOrCreateRoomUseCase(Long userId, Long adminId);
}
