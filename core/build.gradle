plugins {
    id 'java'
    id 'io.spring.dependency-management' version '1.1.6'
}

//jar.enabled = true
bootJar { enabled = false }
jar { enabled = true }
dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation group: 'io.jsonwebtoken', name: 'jjwt-api', version: '0.11.5'
    implementation group: 'io.jsonwebtoken', name: 'jjwt-impl', version: '0.11.5'
    implementation group: 'io.jsonwebtoken', name: 'jjwt-jackson', version: '0.11.5'
    implementation 'org.springframework:spring-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
    testImplementation 'io.projectreactor:reactor-test'// spring-web 의존성 추가
    testImplementation 'io.github.autoparams:autoparams-mockito:9.0.0'
    testImplementation 'io.github.autoparams:autoparams:9.0.0'

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    
    implementation 'com.querydsl:querydsl-jpa:5.0.0:jakarta'
    annotationProcessor "com.querydsl:querydsl-apt:5.0.0:jakarta"
    annotationProcessor "jakarta.annotation:jakarta.annotation-api"
    annotationProcessor "jakarta.persistence:jakarta.persistence-api"
    
    runtimeOnly 'org.postgresql:postgresql'
    runtimeOnly 'org.postgresql:r2dbc-postgresql'
}

repositories {
    mavenCentral()
}

tasks.named('test') {
    useJUnitPlatform()
}

// QueryDSL Q 클래스 생성 설정
def querydslDir = layout.buildDirectory.dir("generated/querydsl")

tasks.withType(JavaCompile).configureEach {
    options.getGeneratedSourceOutputDirectory().set(querydslDir)
}

sourceSets {
    main.java.srcDirs += [querydslDir]
}

clean {
    delete querydslDir
}
