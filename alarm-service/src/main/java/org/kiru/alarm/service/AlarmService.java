package org.kiru.alarm.service;

import static org.kiru.core.exception.code.FailureCode.TOKEN_NOT_ALLOWED;
import static org.kiru.core.exception.code.FailureCode.TOKEN_SENDER_MISSMATCH;

import com.google.firebase.messaging.FirebaseMessagingException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import org.kiru.alarm.dto.request.UpdateDeviceReq;
import org.kiru.alarm.repository.DeviceRepository;
import org.kiru.core.device.domain.Device;
import org.kiru.core.device.entity.DeviceJpaEntity;
import org.kiru.core.exception.EntityNotFoundException;
import org.kiru.core.exception.ForbiddenException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class AlarmService {

    private final DeviceRepository deviceRepository;

    public DeviceJpaEntity createDevice(Device device) {
        DeviceJpaEntity existingDevice = deviceRepository.findByFirebaseToken(device.getFirebaseToken());
        List<DeviceJpaEntity> devices;

        if(existingDevice == null) {
            devices = deviceRepository.findByDeviceId(device.getDeviceId());
            if (devices == null || devices.size() == 0)
                return deviceRepository.save(DeviceJpaEntity.of(device));
        }

        devices = deviceRepository.findByDeviceId(device.getDeviceId());
        deviceRepository.deleteAll(devices);

        return deviceRepository.save(DeviceJpaEntity.of(device));
    }

    public void sendMessageAll(String title, String body, Map<String, String> content) {
        log.info("📢 전체 메시지 전송 시작 - title: {}, body: {}, content: {}", title, body, content);
        List<String> allFirebaseTokens = deviceRepository.findAllDistinctFirebaseTokens();
        sendMessagesToTokens(allFirebaseTokens, title, body, content, "all devices");
    }

    public void sendMessageToUser(Long userId, String title, String body, Map<String, String> content) {
        log.info("📢 사용자 메시지 전송 시작 - userId: {}, title: {}, body: {}, content: {}", userId, title, body, content);
        List<String> firebaseTokens = deviceRepository.findFirebaseTokensByUserId(userId);
        if (!firebaseTokens.isEmpty()) {
            log.info("📱 사용자 디바이스 수: {} - userId: {}", firebaseTokens.size(), userId);
            List<String> validTokens = firebaseTokens.stream()
                    .filter(token -> token != null)
                    .toList();
            log.info("✅ 유효한 토큰 수: {} - userId: {}", validTokens.size(), userId);
            sendMessagesToTokens(validTokens, title, body, content, "user " + userId);
        } else {
            log.warn("⚠️ 사용자 디바이스 없음 - userId: {}", userId);
        }
    }

    private void sendMessagesToTokens(List<String> firebaseTokens, String title, String body, Map<String, String> content, String target) {
        for (String firebaseToken : firebaseTokens) {
            try{
                log.info("📲 메시지 전송 중 - target: {}, token: {}, title: {}, body: {}", target, firebaseToken, title, body);
                sendFcm(firebaseToken, title, body, content);
            } catch (ForbiddenException e){
                DeviceJpaEntity device = deviceRepository.findByFirebaseToken(firebaseToken);
                deviceRepository.delete(device);
            }
        }
    }

    private List<DeviceJpaEntity> findByDeviceId(String deviceId) {
        List<DeviceJpaEntity> devices = deviceRepository.findByDeviceId(deviceId);
        if (devices == null || devices.isEmpty()) {
            log.info("No device found with deviceId: {}", deviceId);
        } else {
            log.info("Found devices: {}", devices);
        }
        return devices;
    }

    private DeviceJpaEntity findDevice(Long userId, String deviceId) {
        DeviceJpaEntity device = deviceRepository.findByUserIdAndDeviceId(userId, deviceId);
        if (device == null) {
            log.info("No device found with userId: {} and deviceId: {}", userId, deviceId);
        } else {
            log.info("Found device: {}", device);
        }
        return device;
    }

    private List<DeviceJpaEntity> findDeviceByUserId(Long userId) {
        List<DeviceJpaEntity> devices = deviceRepository.findByUserId(userId);
        if (devices == null || devices.isEmpty()) {
            log.info("No devices found with userId: {}", userId);
        } else {
            log.info("Found devices: {}", devices);
        }
        return devices;
    }

    private boolean isFirebaseTokenChanged(Device newDevice, DeviceJpaEntity existingDevice){
        return !newDevice.getFirebaseToken().equals(existingDevice.getFirebaseToken());
    }

    private void sendFcm(String firebaseToken, String title, String body, Map<String, String> content) {
        try {
            log.info("🔥 FCM 전송 시도 - token: {}, title: {}, body: {}, content: {}", firebaseToken, title, body, content);
            Notification notification = Notification.builder()
                    .setTitle(title)
                    .setBody(body)
                    .build();

            Message.Builder messageBuilder = Message.builder()
                    .setNotification(notification)
                    .setToken(firebaseToken);

            if (content != null && !content.isEmpty()) {
                messageBuilder.putAllData(content);
            }

            FirebaseMessaging.getInstance().send(messageBuilder.build());
            log.info("✅ FCM 전송 성공 - token: {}", firebaseToken);
        } catch (Exception e) {
            if (e.getMessage().contains("Requested entity was not found"))
                throw new ForbiddenException(TOKEN_NOT_ALLOWED);
            if (e.getMessage().contains("SenderId mismatch")){
                throw new ForbiddenException(TOKEN_SENDER_MISSMATCH);
            }
            log.error("❌ FCM 전송 실패 - token: {}, error: {}", firebaseToken, e.getMessage(), e);
        }
    }
    @Transactional
    public void  updateDevice(UpdateDeviceReq req) {
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        List<DeviceJpaEntity> existingDevices = findByDeviceId(req.getDeviceId());
        for(DeviceJpaEntity deviceJpaEntity: existingDevices) {
            log.info("{} Updating device with userId: {} and deviceId: {}", now,deviceJpaEntity.getUserId(), req.getDeviceId());
           deviceJpaEntity.updateFirebaseToken(req.getFirebaseToken());
        }
        deviceRepository.saveAll(existingDevices);
    }

    @Transactional
    public void deleteDevice(Long userId, String deviceId) {
        DeviceJpaEntity device = findDevice(userId, deviceId);
        if (device != null) {
            deviceRepository.delete(device);
            log.info("Device deleted successfully - userId: {}, deviceId: {}", userId, deviceId);
        } else {
            log.info("Device not found - userId: {}, deviceId: {}", userId, deviceId);
        }
    }
    @Transactional
    public void deleteDeviceByUserId(Long userId) {
        List<DeviceJpaEntity> device = findDeviceByUserId(userId);
        for(DeviceJpaEntity deviceJpaEntity: device) {
            log.info("Deleting device with userId: {} and deviceId: {}", userId, deviceJpaEntity.getDeviceId());
            deviceRepository.delete(deviceJpaEntity);
        }
    }
}
