package org.kiru.chat.event;

import java.util.List;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import org.kiru.chat.adapter.out.persistence.MessageRepository;
import org.kiru.core.chat.message.entity.MessageJpaEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class MessageReadStatusService {
    private final MessageRepository messageRepository;
    
    @PersistenceContext
    private EntityManager entityManager;

    @Transactional
    public void markAllMessagesAsRead(Long chatRoomId, Long userId) {
        // @Modifying 쿼리를 사용하여 직접 업데이트 - 캐시 문제를 방지
        int updatedCount = messageRepository.markMessagesAsRead(chatRoomId, userId);
    }

    public int getUnreadMessageCount(Long chatRoomId, Long userId) {
        return messageRepository.countUnreadMessagesByChatRoomIdAndUserId(chatRoomId, userId);
    }
}
