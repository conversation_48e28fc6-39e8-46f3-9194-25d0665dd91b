package org.kiru.chat.application.service;

import static java.util.Objects.requireNonNull;
import static org.kiru.chat.application.service.RedisStreamKeyManager.createServerKey;

import com.netflix.appinfo.EurekaInstanceConfig;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.core.chat.message.domain.TranslateLanguage;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class WebSocketUserService {
    private final ConcurrentMap<String, Boolean> userConnectionStatus = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, TranslateLanguage> userTranslateStatus = new ConcurrentHashMap<>();
    private final RedisTemplate<String, String> redisTemplateForOne;
    private final EurekaInstanceConfig eurekaInstanceConfig;
    private String INSTANCE_ID;
    private static final String SERVER_KEY_PREFIX = "chat:connected:";
    private static final String ACTIVE_ROOM_KEY_PREFIX = "user:active_room:";


    @PostConstruct
    public final void getServerKey() {
          INSTANCE_ID =eurekaInstanceConfig.getInstanceId();
    }

    public boolean isUserConnected(String userId) {
        String redisKey = createServerKey(userId);
        return redisTemplateForOne.opsForValue().get(redisKey) != null;
    }

    public void updateUserConnectionStatus(final String userId, final boolean isConnected) {
        String redisKey = createServerKey(userId);
        if (isConnected) {
            userConnectionStatus.put(userId, true);
            redisTemplateForOne.opsForValue().set(redisKey, INSTANCE_ID);
        }else{
            userConnectionStatus.remove(userId);
            userTranslateStatus.remove(userId);
            redisTemplateForOne.delete(redisKey);
            removeUserFromChatRoom(userId);
        }
    }

    public List<Long> getConnectedUserIds() {
        List<Long> list = new ArrayList<>();
        Set<String> keys = Optional.ofNullable(redisTemplateForOne.keys(SERVER_KEY_PREFIX+"*"))
            .orElse(Set.of());
        for (String s : keys) {
            try{
                Long parseLong = Long.parseLong(s);
                list.add(parseLong);
            }catch (NumberFormatException e){
                log.error("Failed to parse user id from redis key: {}", s);
            }
        }
        return list;
    }

    public void updateUserTranslationPreference(final String userId,final String targetLanguage) {
        userTranslateStatus.put(userId, TranslateLanguage.valueOf(targetLanguage));
    }

    public TranslateLanguage isUserConnectedAndTranslate(final String userId) {
        requireNonNull(userId, "User ID must be provided");
        if(Boolean.TRUE.equals(userConnectionStatus.getOrDefault(userId, false)) && userTranslateStatus.containsKey(userId)){
            return userTranslateStatus.get(userId);
        }
        return null;
    }
    
    public void setUserInChatRoom(final String userId, final Long chatRoomId) {
        String activeRoomKey = ACTIVE_ROOM_KEY_PREFIX + userId;
        redisTemplateForOne.opsForValue().set(activeRoomKey, chatRoomId.toString());
        log.info("User {} entered chat room {}", userId, chatRoomId);
    }

    public void removeUserFromChatRoom(final String userId) {
        String activeRoomKey = ACTIVE_ROOM_KEY_PREFIX + userId;
        redisTemplateForOne.delete(activeRoomKey);
        log.info("User {} left chat room", userId);
    }

    public boolean isUserInChatRoom(final String userId, final Long chatRoomId) {
        String activeRoomKey = ACTIVE_ROOM_KEY_PREFIX + userId;
        String activeRoomId = redisTemplateForOne.opsForValue().get(activeRoomKey);
        return chatRoomId.toString().equals(activeRoomId);
    }
}