package org.kiru.chat.application.port.out;

import java.util.Optional;

import org.kiru.core.common.CursorPageRequest;
import org.kiru.core.common.CursorPageResponse;
import org.kiru.core.chat.chatroom.domain.ChatRoom;

public interface GetChatRoomQuery {
    Optional<ChatRoom> findById(Long id, boolean isUserAdmin);

    CursorPageResponse<ChatRoom> findRoomsByUserId(Long userId, CursorPageRequest cursorPageRequest);

    ChatRoom getOrCreateRoom(Long userId, Long adminId);

    ChatRoom findAndSetVisible(Long roomId);

    ChatRoom findRoomWithMessagesAndParticipants(Long roomId, Long userId, boolean isUserAdmin);

}
