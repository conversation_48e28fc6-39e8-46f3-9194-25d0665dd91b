package org.kiru.core.common;

import lombok.Getter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@ToString
@Getter
public class CursorPageResponse<T> {
    private final List<T> content;
    private final boolean hasNext;
    private final Long nextCursorId;
    private final LocalDateTime nextCursorCreatedAt;

    public CursorPageResponse(List<T> content, boolean hasNext, Long nextCursorId, LocalDateTime nextCursorCreatedAt) {
        this.content = content;
        this.hasNext = hasNext;
        this.nextCursorId = nextCursorId;
        this.nextCursorCreatedAt = nextCursorCreatedAt;
    }

    public static <T> CursorPageResponse<T> of(List<T> content, boolean hasNext, Long nextCursorId, LocalDateTime nextCursorCreatedAt) {
        return new CursorPageResponse<>(content, hasNext, nextCursorId, nextCursorCreatedAt);
    }

    public static <T, U> CursorPageResponse<U> of(CursorPageResponse<T> source, List<U> newContent) {
        return new CursorPageResponse<>(newContent, source.hasNext, source.nextCursorId, source.nextCursorCreatedAt);
    }
} 