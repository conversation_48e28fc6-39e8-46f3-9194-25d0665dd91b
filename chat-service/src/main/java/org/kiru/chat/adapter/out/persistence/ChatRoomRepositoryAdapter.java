package org.kiru.chat.adapter.out.persistence;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.chat.adapter.in.web.res.AdminUserResponse;
import org.kiru.core.chat.chatroom.entity.QChatRoomJpaEntity;
import org.kiru.core.chat.message.entity.QMessageJpaEntity;
import org.kiru.core.chat.userchatroom.entity.QUserJoinChatRoom;
import org.kiru.core.common.CursorPageRequest;
import org.kiru.core.common.CursorPageResponse;
import org.kiru.chat.adapter.out.persistence.dto.ChatRoomWithDetails;
import org.kiru.chat.application.port.out.GetAlreadyLikedUserIdsQuery;
import org.kiru.chat.application.port.out.GetChatRoomQuery;
import org.kiru.chat.application.port.out.SaveChatRoomPort;
import org.kiru.core.chat.chatroom.domain.ChatRoom;
import org.kiru.core.chat.chatroom.domain.ChatRoomType;
import org.kiru.core.chat.chatroom.entity.ChatRoomJpaEntity;
import org.kiru.core.chat.message.domain.Message;
import org.kiru.core.chat.message.entity.MessageJpaEntity;
import org.kiru.core.chat.userchatroom.entity.UserJoinChatRoom;
import org.kiru.core.exception.EntityNotFoundException;
import org.kiru.core.exception.ForbiddenException;
import org.kiru.core.exception.code.FailureCode;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.JPAExpressions;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.stream.Collectors;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;

@Slf4j
@Repository
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ChatRoomRepositoryAdapter implements GetChatRoomQuery, SaveChatRoomPort, GetOtherParticipantQuery,
        GetAlreadyLikedUserIdsQuery {
    private final ChatRoomRepository chatRoomRepository;
    private final UserJoinChatRoomRepository userJoinChatRoomRepository;
    private final JPAQueryFactory queryFactory;

    @Transactional
    public ChatRoom save(ChatRoom chatRoom, Long userId, Long userId2) {
        List<UserJoinChatRoom> rooms = userJoinChatRoomRepository.findAllByUserIds(userId, userId2);
        if (!rooms.isEmpty()) {
            log.info("Chat room already exist for user {} and user {}", userId, userId2);
            Long roomId = rooms.get(0).getChatRoomId();
            return chatRoomRepository.findById(roomId)
                .map(ChatRoomJpaEntity::toModel)
                .orElseThrow(() -> new EntityNotFoundException(FailureCode.CHATROOM_NOT_FOUND));
        }

        ChatRoomJpaEntity entity = ChatRoomJpaEntity.of(chatRoom);
        ChatRoomJpaEntity chatRoomJpa = chatRoomRepository.save(entity);
        UserJoinChatRoom userFirst = UserJoinChatRoom.builder()
                .chatRoomId(chatRoomJpa.getId())
                .userId(userId)
                .build();
        UserJoinChatRoom userSecond = UserJoinChatRoom.builder()
                .chatRoomId(chatRoomJpa.getId())
                .userId(userId2)
                .build();
        userJoinChatRoomRepository.saveAll(List.of(userFirst, userSecond));
        return ChatRoomJpaEntity.toModel(chatRoomJpa);
    }

    public Optional<ChatRoom> findById(Long id, boolean isUserAdmin) {
        if (isUserAdmin) {
            return chatRoomRepository.findById(id)
                    .map(ChatRoomJpaEntity::toModel);
        }
        return chatRoomRepository.findById(id)
                .filter(ChatRoomJpaEntity::getVisible)
                .map(ChatRoomJpaEntity::toModel);
    }

    public CursorPageResponse<ChatRoom> findRoomsByUserId(Long userId, CursorPageRequest cursorPageRequest) {
        // 사용자가 참여한 채팅방 ID 목록 조회
        List<Long> userChatRoomIds = getUserChatRoomIds(userId);
        if (userChatRoomIds.isEmpty()) {
            return CursorPageResponse.of(List.of(), false, null, null);
        }
        
        // 데이터베이스에서 정렬 및 커서 필터링 수행
        List<Tuple> chatRoomWithLatestMessage = getChatRoomsWithLatestMessageTimeSorted(userChatRoomIds, cursorPageRequest);
        
        boolean hasNext = chatRoomWithLatestMessage.size() > cursorPageRequest.getSize();
        if (hasNext) {
            chatRoomWithLatestMessage = chatRoomWithLatestMessage.subList(0, cursorPageRequest.getSize());
        }
        
        // 채팅방 상세 정보 설정
        List<ChatRoom> chatRooms = enrichChatRoomDetails(chatRoomWithLatestMessage, userId);

        // 커서 정보 생성 및 응답 반환
        return generateCursorPageResponse(chatRooms, hasNext, chatRoomWithLatestMessage);
    }

    private List<Long> getUserChatRoomIds(Long userId) {
        QUserJoinChatRoom uj = QUserJoinChatRoom.userJoinChatRoom;
        return queryFactory
            .select(uj.chatRoomId)
            .from(uj)
            .where(uj.userId.eq(userId).and(uj.isActive.isTrue()))
            .fetch();
    }

    private List<Tuple> getChatRoomsWithLatestMessageTimeSorted(List<Long> userChatRoomIds, CursorPageRequest cursorPageRequest) {
        QChatRoomJpaEntity cr = QChatRoomJpaEntity.chatRoomJpaEntity;
        QMessageJpaEntity m = QMessageJpaEntity.messageJpaEntity;
        
        // 기본 쿼리 빌더
        var query = queryFactory
            .select(cr.id, cr.createdAt, m.createdAt.max())
            .from(cr)
            .leftJoin(m).on(m.chatRoomId.eq(cr.id))
            .where(cr.id.in(userChatRoomIds))
            .groupBy(cr.id, cr.createdAt);
            
        // 커서 조건을 HAVING 절에 추가
        BooleanExpression havingCondition = buildCursorHavingCondition(
            cursorPageRequest.getCursorId(), 
            cursorPageRequest.getCursorCreatedAt()
        );
        
        if (havingCondition != null) {
            query = query.having(havingCondition);
        }
        
        // 정렬 및 제한
        return query
            .orderBy(
                // 최신 메시지 시간이 있으면 그것으로, 없으면 채팅방 생성시간으로 정렬 (내림차순)
                Expressions.cases()
                    .when(m.createdAt.max().isNotNull())
                    .then(m.createdAt.max())
                    .otherwise(cr.createdAt).desc(),
                cr.id.desc() // 채팅방 ID 내림차순
            )
            .limit(cursorPageRequest.getSize() + 1)
            .fetch();
    }

    private BooleanExpression buildCursorHavingCondition(Long cursorId, LocalDateTime cursorCreatedAt) {
        if (cursorId == null || cursorCreatedAt == null) {
            log.debug("Cursor parameters are null - cursorId: {}, cursorCreatedAt: {}", cursorId, cursorCreatedAt);
            return null;
        }
        
        try {
            QChatRoomJpaEntity cr = QChatRoomJpaEntity.chatRoomJpaEntity;
            QMessageJpaEntity m = QMessageJpaEntity.messageJpaEntity;
            
            // 비교할 시간 (최신 메시지 시간이 있으면 그것, 없으면 채팅방 생성시간)
            var compareTime = Expressions.cases()
                .when(m.createdAt.max().isNotNull())
                .then(m.createdAt.max())
                .otherwise(cr.createdAt);
            
            // 커서 조건: 시간이 이전이거나, 같은 시간이면 ID가 더 작은 것
            return compareTime.lt(cursorCreatedAt)
                .or(compareTime.eq(cursorCreatedAt).and(cr.id.lt(cursorId)));
        } catch (Exception e) {
            log.warn("Error building cursor having condition for cursorId: {}, cursorCreatedAt: {}", 
                    cursorId, cursorCreatedAt, e);
            return null;
        }
    }

    private List<ChatRoom> enrichChatRoomDetails(List<Tuple> finalResults, Long userId) {
        List<Long> chatRoomIds = finalResults.stream()
            .map(result -> result.get(0, Long.class))
            .toList();

        QChatRoomJpaEntity cr = QChatRoomJpaEntity.chatRoomJpaEntity;
        QMessageJpaEntity m = QMessageJpaEntity.messageJpaEntity;
        QUserJoinChatRoom uj = QUserJoinChatRoom.userJoinChatRoom;

        // 채팅방 엔티티 조회
        List<ChatRoomJpaEntity> chatRoomEntities = queryFactory
            .selectFrom(cr)
            .where(cr.id.in(chatRoomIds))
            .fetch();

        Map<Long, ChatRoomJpaEntity> chatRoomMap = chatRoomEntities.stream()
            .collect(Collectors.toMap(ChatRoomJpaEntity::getId, entity -> entity));

        // 읽지 않은 메시지 수 조회
        Map<Long, Integer> unreadCounts = queryFactory
            .select(m.chatRoomId, m.id.count().intValue())
            .from(m)
            .where(
                m.chatRoomId.in(chatRoomIds)
                    .and(m.readStatus.isFalse())
                    .and(m.senderId.ne(userId))
            )
            .groupBy(m.chatRoomId)
            .fetch()
            .stream()
            .collect(Collectors.toMap(
                tuple -> tuple.get(0, Long.class),
                tuple -> tuple.get(1, Integer.class)
            ));

        // 최신 메시지 내용 조회
        Map<Long, String> latestMessageContents = queryFactory
            .select(m.chatRoomId, m.content)
            .from(m)
            .where(
                m.chatRoomId.in(chatRoomIds)
                    .and(m.id.in(
                        JPAExpressions
                            .select(m.id.max())
                            .from(m)
                            .where(m.chatRoomId.in(chatRoomIds)
                                .and(m.createdAt.in(
                                    JPAExpressions
                                        .select(m.createdAt.max())
                                        .from(m)
                                        .where(m.chatRoomId.in(chatRoomIds))
                                        .groupBy(m.chatRoomId)
                                )))
                            .groupBy(m.chatRoomId)
                    ))
            )
            .fetch()
            .stream()
            .collect(Collectors.toMap(
                tuple -> tuple.get(0, Long.class),
                tuple -> tuple.get(1, String.class)
            ));

        // 참가자 목록 조회 (현재 사용자 제외)
        Map<Long, List<Long>> participantsMap = queryFactory
            .select(uj.chatRoomId, uj.userId)
            .from(uj)
            .where(
                uj.chatRoomId.in(chatRoomIds)
                    .and(uj.isActive.isTrue())
                    .and(uj.userId.ne(userId))
            )
            .fetch()
            .stream()
            .collect(Collectors.groupingBy(
                tuple -> tuple.get(0, Long.class),
                Collectors.mapping(
                    tuple -> tuple.get(1, Long.class),
                    Collectors.toList()
                )
            ));

        return finalResults.stream().map(result -> {
            Long chatRoomId = result.get(0, Long.class);
            ChatRoomJpaEntity chatRoomEntity = chatRoomMap.get(chatRoomId);
            ChatRoom chatRoom = ChatRoomJpaEntity.toModel(chatRoomEntity);
            
            int unreadMessageCount = unreadCounts.getOrDefault(chatRoomId, 0);
            String latestMessageContent = latestMessageContents.get(chatRoomId);
            List<Long> participants = participantsMap.getOrDefault(chatRoomId, List.of());

            chatRoom.addParticipants(participants);
            chatRoom.setUnreadMessageCount(unreadMessageCount);
            chatRoom.setLatestMessageContent(latestMessageContent);
            return chatRoom;
        }).toList();
    }

    private CursorPageResponse<ChatRoom> generateCursorPageResponse(List<ChatRoom> chatRooms, boolean hasNext, List<Tuple> finalResults) {
        if (!hasNext || finalResults.isEmpty()) {
            return CursorPageResponse.of(chatRooms, hasNext, null, null);
        }
        
        Tuple lastResult = finalResults.get(finalResults.size() - 1);
        Long nextCursorId = lastResult.get(0, Long.class);
        LocalDateTime lastMessageTime = lastResult.get(2, LocalDateTime.class);
        LocalDateTime lastChatRoomCreated = lastResult.get(1, LocalDateTime.class);
        
        LocalDateTime nextCursorCreatedAt = lastMessageTime != null ? lastMessageTime : lastChatRoomCreated;
        
        return CursorPageResponse.of(chatRooms, hasNext, nextCursorId, nextCursorCreatedAt);
    }

    @Transactional
    public ChatRoom getOrCreateRoom(Long userId, Long adminId) {
        List<UserJoinChatRoom> userChatRoomExist = userJoinChatRoomRepository.findByUserIdAndAdminId(userId, adminId);
        if (!userChatRoomExist.isEmpty()) {
            return chatRoomRepository.findById(userChatRoomExist.getFirst().getChatRoomId())
                    .map(ChatRoomJpaEntity::toModel)
                    .orElseThrow(() -> new IllegalStateException("Chat room not found"));
        } else {
            ChatRoom chatRoom = ChatRoom.of("CONTACTO MANAGER", ChatRoomType.PRIVATE);
            ChatRoomJpaEntity chatRoomJpa = chatRoomRepository.saveAndFlush(ChatRoomJpaEntity.of(chatRoom, false));
            userJoinChatRoomRepository.saveAll(
                    List.of(
                            UserJoinChatRoom.builder()
                                    .chatRoomId(chatRoomJpa.getId())
                                    .userId(adminId)
                                    .build(),
                            UserJoinChatRoom.builder()
                                    .chatRoomId(chatRoomJpa.getId())
                                    .userId(userId)
                                    .build()
                    )
            );
            return ChatRoomJpaEntity.toModel(chatRoomJpa);
        }
    }

    @Override
    @Cacheable(value = "chatRoom", key = "#roomId", unless = "#result == null")
    public ChatRoom findAndSetVisible(Long roomId) {
        ChatRoomJpaEntity chatRoomJpaEntity = chatRoomRepository.findById(roomId).orElseThrow(
                () -> new EntityNotFoundException(FailureCode.CHATROOM_NOT_FOUND));
        chatRoomJpaEntity.setVisible(true);
        return ChatRoomJpaEntity.toModel(chatRoomJpaEntity);
    }

    @Transactional
    public ChatRoom findRoomWithMessagesAndParticipants(Long roomId, Long userId, boolean isUserAdmin) {
        List<ChatRoomWithDetails> results = isUserAdmin ?
                chatRoomRepository.findRoomWithMessagesAndParticipantsByAdmin(roomId)
                        .orElseThrow(() -> new EntityNotFoundException(FailureCode.CHATROOM_NOT_FOUND)) :
                chatRoomRepository.findRoomWithMessagesAndParticipants(roomId)
                        .orElseThrow(() -> new EntityNotFoundException(FailureCode.CHATROOM_NOT_FOUND));
        if (results.isEmpty()) {
            throw new EntityNotFoundException(FailureCode.CHATROOM_NOT_FOUND);
        }
        return getChatRoomByUserIdAndIsUserAdmin(userId, isUserAdmin, results, ChatRoomJpaEntity.toModel(results.getFirst().chatRoom()));
    }

    private ChatRoom getChatRoomByUserIdAndIsUserAdmin(Long userId, boolean isUserAdmin,
                                                       List<ChatRoomWithDetails> results, ChatRoom chatRoom) {
        List<Long> participants = results.stream()
                .map(ChatRoomWithDetails::userId)
                .toList();
        if(!isUserAdmin && !participants.contains(userId)){
            throw new ForbiddenException(FailureCode.CHAT_ROOM_ACCESS_DENIED);
        }
        chatRoom.addParticipants(participants);
        List<Message> messages;
        if(participants.contains(userId)){
            List<MessageJpaEntity> resultsMessages = results.stream()
                    .map(ChatRoomWithDetails::message)
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();
            
            messages = resultsMessages.stream().map(MessageJpaEntity::toModel).toList();
            chatRoom.removeParticipant(userId);
        }else{
            messages = results.stream()
                    .map(ChatRoomWithDetails::message)
                    .map(MessageJpaEntity::toModel)
                    .distinct()
                    .filter(Objects::nonNull)
                    .toList();
        }
        chatRoom.addMessage(messages);
        return chatRoom;
    }

    public Long getOtherParticipantId(Long roomId, Long senderId) {
        List<Long> otherParticipantIds = userJoinChatRoomRepository.findOtherParticipantIds(roomId, senderId);
        return otherParticipantIds.isEmpty() ? null : otherParticipantIds.getFirst();
    }

    public List<Long> getAlreadyLikedUserIds(Long userId) {
        return userJoinChatRoomRepository.findAlreadyLikedUserIds(userId);
    }

    public List<AdminUserResponse> getMatchedUsers(Long userId) {
        return userJoinChatRoomRepository.getMatchedUser(userId);
    }
}